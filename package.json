{"name": "homestead-ui", "version": "0.2.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^16.3.0", "chart.js": "^4.4.0", "jwt-decode": "^4.0.0", "moment": "^2.29.4", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "^5.0.1", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/jest": "^29.5.14", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^3.4.17", "typescript": "^4.9.5"}}