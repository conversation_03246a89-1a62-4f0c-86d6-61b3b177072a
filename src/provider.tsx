import React, { createContext, useContext, useEffect } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { validateToken } from "./libs/user";
import { useLocalStorage } from "./hooks/useLocalStorage";

export const TokenContext = createContext("");

export function Providers({ children }: {
    children: React.ReactNode
}) {
    const accessTokenKey = process.env.REACT_ACCESS_TOKEN_STORAGE || "access_token";
    const [token, setToken] = useLocalStorage(accessTokenKey, "");
    const [searchParams] = useSearchParams();
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const tokenQuery = searchParams.get("token") || "";
        
        if (validateToken(tokenQuery)) {
            setToken(tokenQuery);
            // Use React Router's navigation
            navigate(location.pathname, { replace: true });
        } else if (!validateToken(token)) {
            navigate("/401", { replace: true });
        }
    }, [searchParams, navigate, location.pathname, setToken]);

    return (
        <TokenContext.Provider value={token}>
            {children}
        </TokenContext.Provider>
    );

}

export const useAuth = () => {
    return useContext(TokenContext);
};
