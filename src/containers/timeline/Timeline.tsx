import { useContext, useEffect, useState } from "react";
import ConfirmDlg from "../../components/dialog/ConfirmDlg";
import Button from "../../components/form/Button";
import AlertBox from "../../components/dialog/AlertBox";
import { Link, useNavigate } from "react-router-dom";
import { handleServerError } from "../../models/Response";
import { TokenContext } from "../../provider";
import { Loading } from "../../components/Loading";

const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
export default function Timeline() {
    const token = useContext(TokenContext);
    const navigate = useNavigate();
    const [data, setData] = useState([])
    const [confirmDelete, setConfirmDelete] = useState("")
    const [deleteMsg, setDeleteMsg] = useState("")
    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/daily`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data) => {
                    setData(data);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token]);


    function setConfirmOpen(id: string, open: boolean) {
        if (open) setConfirmDelete(id);
        else setConfirmDelete("");
    }

    function deleteTimeline(id: string) {
        try {
            fetch(process.env.REACT_APP_API_URL + `/daily`, {
                method: 'DELETE',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: id })
            })
                .then((res) => res.json())
                .then((data) => {
                    if (data.error) {
                        setDeleteMsg(handleServerError(data.error.message))
                    } else {
                        setDeleteMsg("");
                        navigate(0);
                    }
                });
        } catch (err) {
            console.log(err);
        }
    }

    function uuidv4() {
        return (`${1e7}-${1e3}-${4e3}-${8e3}-${1e11}`).replace(/[018]/g, (c: any) =>
            (((c ^ crypto.getRandomValues(new Uint8Array(1))[0]) & 15) >> c / 4).toString(16)
        );
    }
    if (data.length === 0) {
        return <Loading />
    }

    return (
        <>
            {Array.isArray(data) ?
                data.map((tl: any) => (
                    <ConfirmDlg
                        key={tl.id}
                        id={tl.id}
                        open={confirmDelete === tl.id}
                        title={`Delete reminder at ${tl.time}?`}
                        onClose={() => setConfirmOpen(tl.id, false)}
                        onConfirm={deleteTimeline}
                    >
                        Are you sure you want to delete this timeline &quot;{tl.description}&quot;?
                    </ConfirmDlg>

                )) : null}
            <AlertBox id={uuidv4()} onClose={(_) => setDeleteMsg("")} visible={deleteMsg !== ""}>
                {deleteMsg}
            </AlertBox>
            <ol className="relative border-l border-gray-200 dark:border-gray-700">
                {Array.isArray(data) ?
                    data.map((tl: any) => (
                        <li key={uuidv4()} className="mb-10 ml-6">
                            <span className="absolute flex items-center justify-center w-6 h-6 bg-white rounded-full -left-3 ring-8 ring-white">
                                {tl.command ?
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                                    </svg>
                                    :
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0M3.124 7.5A8.969 8.969 0 015.292 3m13.416 0a8.969 8.969 0 012.168 4.5" />
                                    </svg>
                                }
                            </span>
                            <time className="block mb-2 text-sm font-normal leading-none text-black">{tl.time}</time>
                            <p className="text-base mb-2 font-normal text-slate-700">{tl.description}</p>
                            <div className="grid grid-cols-3">
                                {tl.dates.map((date: number) => (
                                    <span key={uuidv4()} className="bg-gray-100 my-1 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">{days[date]}</span>
                                ))}
                            </div>
                            <div className="space-x-2 my-2">
                                <Link className="inline-flex items-center text-white text-sm border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 focus:text-blue-700 py-2 px-4 focus:outline-none focus:shadow-outline font-bold bg-blue-500" to={`/edit-timeline/${tl.id}`}>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                    </svg>
                                    Edit
                                </Link>
                                <Button onClick={() => setConfirmOpen(tl.id, true)} className="bg-red-800 text-red-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                    </svg>
                                    Delete
                                </Button>
                            </div>
                        </li>
                    )) : null}
            </ol>
        </>
    )
}