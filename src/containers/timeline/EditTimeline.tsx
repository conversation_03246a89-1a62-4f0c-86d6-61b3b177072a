import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useAuth } from "../../provider";
import { Timeline } from "../../models/Timeline";
import TimelineForm from "../../components/form/TimelineForm";
import { Loading } from "../../components/Loading";


export default function EditTimeline() {
    const token = useAuth();
    let { id } = useParams();
    const [timeline, setTimeline] = useState<Timeline>()
    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/daily`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: Timeline[]) => {
                    data.forEach((item) => {
                        if (item.id === id) {
                            setTimeline({ ...item });
                        }
                    })
                });
        } catch (err) {
            console.log(err);
        }
    }, [id, token])
    if (!timeline) return <Loading />

    return <TimelineForm value={timeline} />
}