import { useEffect, useState } from "react";
import Histogram from "../../components/chart/Histogram";
import { useParams } from "react-router-dom";
import { useAuth } from "../../provider";
import { Loading } from "../../components/Loading";
import { StockStat } from "../../models/Stock";
import { DataPoint } from "../../models/Chart";

export default function PriceDistribution() {
    let { code } = useParams();
    const token = useAuth();

    const priceSixMonthDataSet = {
        dataTitle: "6 months P/L",
        distribution: "sixmonthdist",
        bgColor: "rgb(255, 99, 132)",
    }
    const priceOneYearDataSet = {
        dataTitle: "1 year P/L",
        distribution: "oneyeardist",
        bgColor: "rgb(53, 162, 235)",
    };

    const dividendDataSet = {
        dataTitle: "Div percentage",
        distribution: "dividendist",
        bgColor: "rgb(75, 192, 192)",
    };
    const [halfPriceDataPoint, setHalfPriceDataPoint] = useState<DataPoint>();
    const [priceDataPoint, setPriceDataPoint] = useState<DataPoint>();
    const [divDataPoint, setDivDataPoint] = useState<DataPoint>();
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        document.title = code + " distribution";
        try {
            const params = new URLSearchParams({ code: code! });
            fetch(process.env.REACT_APP_API_URL + `/stat/stock?` + params.toString(), {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: StockStat) => {
                    setHalfPriceDataPoint({
                        code: code!,
                        x: ((data.sd_price / data.price) * 100) / 2,
                        negativeColor: "rgb(255, 71, 26)",
                        positiveColor: "rgb(51, 51, 255)"
                    });
                    setPriceDataPoint({
                        code: code!,
                        x: (data.sd_price / data.price) * 100,
                        negativeColor: "rgb(255, 71, 26)",
                        positiveColor: "rgb(51, 51, 255)"
                    });
                    setDivDataPoint({
                        code: code!,
                        x: (data.dividend / data.price) * 100,
                        negativeColor: "rgb(255, 71, 26)",
                        positiveColor: "rgb(51, 51, 255)"
                    });
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token, code]);

    if (loading) {
        return <Loading />
    }

    return <>
        <Histogram title="Price deviances" dataPoint={halfPriceDataPoint} dataSet={priceSixMonthDataSet} xSuffix="%" />
        <Histogram title="Price deviances" dataPoint={priceDataPoint} dataSet={priceOneYearDataSet} xSuffix="%" />
        <Histogram title="Dividend distribution" dataPoint={divDataPoint} dataSet={dividendDataSet} xSuffix="%" />
    </>
}