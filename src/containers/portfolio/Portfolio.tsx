
import { useContext, useEffect, useState } from "react";
import { TokenContext } from "../../provider";
import { PortfolioStock } from "../../models/Stock";
import PortfolioStat from "../../components/card/PortfolioStat";
import { PortfolioValue } from "../../models/Portfolio";
import { StockStat } from "../../components/card/StockStat";
import { Loading } from "../../components/Loading";

export default function Portfolio() {
    const token = useContext(TokenContext);
    const [loading, setLoading] = useState(true);
    const [porfolioValue, setPortfolioValue] = useState<PortfolioValue>({
        fair_value: 0,
        book_value: 0,
        dividend: 0,
        mean_dividend: 0,
        cash: 0,
        fair_no_cash_value: 0,
    });

    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/portfolio/fair-values`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: PortfolioStock[]) => {
                    let totalFairValue = 0;
                    let totalBookValue = 0;
                    let totalDividend = 0;
                    let totalMeanDividend = 0;
                    let cash = 0;
                    data.forEach((stock) => {
                        totalFairValue += stock.fair_value;
                        totalBookValue += stock.book_value - stock.total_dividend;
                        totalDividend += stock.dividend * stock.amount;
                        totalMeanDividend += stock.mean_dividend * stock.amount;
                        if (stock.code === "CASH") {
                            cash = stock.fair_value;
                        }
                    })
                    const pval: PortfolioValue = {
                        fair_value: totalFairValue,
                        book_value: totalBookValue,
                        dividend: totalDividend,
                        mean_dividend: totalMeanDividend,
                        cash: cash,
                        fair_no_cash_value: totalFairValue - cash,
                    }
                    setPortfolioValue(pval);
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token, setPortfolioValue, setLoading])

    if (loading) {
        return <Loading />
    }
    return (
        <div>
            <div className="grid grid-flow-row grid-cols-1 gap-2">
                <h1 className="text-2xl md:text-3xl pl-2 my-2 border-l-4  font-sans font-bold border-blue-400">
                    Selling highlights
                </h1>
                <StockStat />
                <h1 className="text-2xl md:text-3xl pl-2 my-2 border-l-4  font-sans font-bold border-blue-400">
                    Portfolio value
                </h1>
                <PortfolioStat title="Book value" link="/invested-stock" displayValue={porfolioValue.book_value} percentValue={porfolioValue.fair_no_cash_value / porfolioValue.book_value - 1} />
                <PortfolioStat title="Market price no cash" displayValue={porfolioValue.fair_no_cash_value} percentValue={porfolioValue.fair_no_cash_value / porfolioValue.book_value - 1} />
                <PortfolioStat title="Market price with cash" displayValue={porfolioValue.fair_value} percentValue={porfolioValue.fair_value / porfolioValue.book_value - 1} />
                <PortfolioStat title="Accumulate cash" link="/portfolio-cash-events" displayValue={porfolioValue.cash} percentValue={porfolioValue.cash / porfolioValue.book_value} />
                <PortfolioStat title="Annual Dividend" displayValue={porfolioValue.dividend} percentValue={porfolioValue.dividend / porfolioValue.book_value} />
                <PortfolioStat title="Annual Mean Dividend" displayValue={porfolioValue.mean_dividend} percentValue={porfolioValue.mean_dividend / porfolioValue.book_value} />

            </div>
        </div>
    )
}