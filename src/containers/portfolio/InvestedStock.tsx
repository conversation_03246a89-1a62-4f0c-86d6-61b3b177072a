import { useContext, useEffect, useState } from "react";
import { TokenContext } from "../../provider";
import { PortfolioStock } from "../../models/Stock";
import { StockCard } from "../../components/card/StockCard";
import { Loading } from "../../components/Loading";

export default function InvestedStock() {
    const token = useContext(TokenContext);
    const [loading, setLoading] = useState(true);
    const [data, setData] = useState<PortfolioStock[]>([]);
    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/portfolio/fair-values`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: PortfolioStock[]) => {
                    setData(data);
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token, setData, setLoading]);

    if (loading) {
        return <Loading />
    }
    return (
        <div>
            <div className="grid grid-flow-row grid-cols-1 gap-2">
                {data.map((stock) => (
                    stock.code !== "CASH" ? <StockCard info={stock} /> : null
                ))}
            </div>
        </div>
    )
}