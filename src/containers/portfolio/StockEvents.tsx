import { useParams } from "react-router-dom";
import { useAuth } from "../../provider";
import { useEffect, useState } from "react";
import { PorfolioStockEvent } from "../../models/Portfolio";
import { Loading } from "../../components/Loading";
import { IconMoney } from "../../components/icon/IconMoney";
import { IconCart } from "../../components/icon/IconCart";
import { formatCurrency, formatNumber } from "../../libs/numberUtil";
import moment from "moment";

export default function StockEvents() {
    const token = useAuth();
    let { code } = useParams();
    const [data, setData] = useState<PorfolioStockEvent[]>([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        try {
            const params = new URLSearchParams({ code: code || "" });
            fetch(process.env.REACT_APP_API_URL + `/portfolio/events?` + params.toString(), {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: PorfolioStockEvent[]) => {
                    setData(data);
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [code, token]);

    if (loading) {
        return <Loading />
    }

    return <>
        <ol className="relative border-l border-gray-200">
            {data.map((event: PorfolioStockEvent) => (
                <li className="mb-10 ml-6">
                    <span className="absolute flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full -left-3 ring-8 ring-white ">
                        {
                            event.category === "stock" ?
                                <IconCart className="rounded-full" /> : <IconMoney className="rounded-full" />
                        }
                    </span>
                    <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm ">
                        <div className="items-center justify-between mb-3 sm:flex">
                            <time className="mb-1 text-xs font-normal text-gray-400 sm:order-last sm:mb-0">{moment(event.buy_at).fromNow()}</time>
                            <div className="text-sm font-normal text-gray-500 lex ">{event.category === "stock" ?
                                event.quantity > 0 ? "Buy" : "Sell" : "Received dividends from"} <span className="font-semibold text-blue-600">{event.code}</span> <span className="bg-gray-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded ">{event.quantity > 0 ? formatCurrency(event.price * event.quantity * (1 + event.tax / 100), "VND") : formatCurrency(event.price * event.quantity * (1 - event.tax / 100), "VND")}</span></div>
                        </div>
                        <div className="p-3 text-xs italic font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 ">
                            <p>Total quantity: {formatNumber(event.quantity)}</p>
                            {event.category === "stock" ?
                                <p>Unit price: {formatCurrency(event.price, "VND")}</p> :
                                <p>Cash: {formatCurrency(event.price, "VND")}</p>
                            }
                            <p>Tax: {formatNumber(event.tax)}%</p>
                        </div>
                    </div>
                </li>
            ))}
        </ol>

    </>
}