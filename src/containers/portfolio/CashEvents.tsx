import { useAuth } from "../../provider";
import { useEffect, useState } from "react";
import { PorfolioCashEvent } from "../../models/Portfolio";
import { Loading } from "../../components/Loading";
import { IconMoney } from "../../components/icon/IconMoney";
import { IconCart } from "../../components/icon/IconCart";
import { formatCurrency } from "../../libs/numberUtil";
import moment from "moment";

export default function CashEvents() {
    const token = useAuth();
    const [data, setData] = useState<PorfolioCashEvent[]>([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/portfolio/cash-events`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: PorfolioCashEvent[]) => {
                    setData(data);
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token]);

    if (loading) {
        return <Loading />
    }

    return <>
        <ol className="relative border-l border-gray-200">
            {data.map((event: PorfolioCashEvent) => (
                <li className="mb-10 ml-6">
                    <span className="absolute flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full -left-3 ring-8 ring-white ">
                        {
                            event.category === "stock" ?
                                <IconCart className="rounded-full" /> : <IconMoney className="rounded-full" />
                        }
                    </span>
                    <div className="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex ">
                        <time className="mb-1 text-xs font-normal text-gray-400 sm:order-last sm:mb-0">{moment(event.record_at).fromNow()}</time>
                        <div className="text-sm font-normal text-gray-500 ">{event.category === "stock" ?
                            event.quantity > 0 ? "Gain" : "Loss" : "Received dividends from"} <span className="font-semibold text-blue-600">{event.code}</span> <span className="bg-gray-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded ">{formatCurrency(event.quantity, "VND")}</span></div>
                    </div>
                </li>
            ))}
        </ol>

    </>
}