import { Routes, Route } from "react-router-dom";
import Timeline from "./containers/timeline/Timeline";
import NoPage from "./components/NoPage";
import Portfolio from "./containers/portfolio/Portfolio";
import { ProtectedLayout } from "./ProtectedLayout";
import TimelineForm from "./components/form/TimelineForm";
import EditTimeline from "./containers/timeline/EditTimeline";
import InvestedStock from "./containers/portfolio/InvestedStock";
import StockEvents from "./containers/portfolio/StockEvents";
import CashEvents from "./containers/portfolio/CashEvents";
import PriceDistribution from "./containers/stat/PriceDistribution";

function Root() {
  return (
    <Routes>
      <Route path="/" element={<ProtectedLayout />}>
        <Route index element={<Timeline />} />
        <Route path="portfolio" element={<Portfolio />} />
        <Route path="add-timeline" element={<TimelineForm />} />
        <Route path="edit-timeline/:id" element={<EditTimeline />} />
        <Route path="invested-stock" element={<InvestedStock />} />
        <Route path="portfolio-stock-events/:code" element={<StockEvents />} />
        <Route path="portfolio-cash-events" element={<CashEvents />} />
        <Route path="distribution/:code" element={<PriceDistribution />} />
      </Route>
      <Route path="*" element={<NoPage />} />
    </Routes>
  );
}

export default Root;
