export interface PortfolioStock {
    code: string
    price: number
    dividend: number
    mean_dividend: number
    normal_buy_price: number
    amount: number
    fair_value: number
    book_value: number
    total_dividend: number
    buy_at: string
}

export interface PortfolioWatching {
    code: string
    price: number
    book_price: number
    dividend: number
    mean_price: number
    sd_price: number
    peak_price: number
    bottom_price: number
    buy_at: string
}

export interface StockStat {
    dividend: number
    mean_price: number
    price: number
    sd_price: number
}
