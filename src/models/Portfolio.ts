export interface PortfolioValue {
    fair_value: number
    fair_no_cash_value: number
    book_value: number
    cash: number
    dividend: number
    mean_dividend: number
}

export interface PorfolioStockEvent {
    code: string
    category: string
    quantity: number
    price: number
    tax: number
    buy_at: string
}

export interface PorfolioCashEvent {
    code: string
    category: string
    quantity: number
    record_at: string
}


