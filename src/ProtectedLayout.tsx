import NavBar from "./components/Navbar";
import { useOutlet } from "react-router-dom";
import { Providers } from "./provider";

export const ProtectedLayout = () => {
    const outlet = useOutlet();
    return (
        <Providers>
            <div className="max-w-3xl mx-auto p-4">
                <NavBar />
                <div className="border p-5 border-slate-300">
                    {outlet}
                </div>
            </div>
        </Providers>
    );
};