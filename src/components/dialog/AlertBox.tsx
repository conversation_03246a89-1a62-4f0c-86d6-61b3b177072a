import { Transition } from "@headlessui/react";
import { MouseEvent, ReactNode, useEffect } from "react";

interface Props {
    id: string;
    children: ReactNode;
    visible?: boolean;
    onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
    onClose?: (key: string) => void;
    className?: string;
}

export default function AlertBox(props: Props) {
    const { id, children, visible, onClose } = props;
    useEffect(() => {
        let timer = setTimeout((() => {
            if (onClose) onClose(id);
        }), 3000);
        return () => clearTimeout(timer);
    }, [id, onClose]);
    if (!visible) {
        return <></>
    }
    return (
        <Transition
            show={visible}
            enter="transition-opacity duration-75"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity duration-150"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
        >
            <div className="flex fixed top-5 right-5 shadow-lg z-10 items-center w-full max-w-xs p-4 space-x-4 text-gray-500 bg-white divide-x divide-gray-200 rounded-lg dark:text-gray-400 dark:divide-gray-700 space-x dark:bg-gray-800" role="alert">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-500 rotate-45" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m9 17 8 2L9 1 1 19l8-2Zm0 0V9" />
                </svg>
                <div className="pl-4 text-sm font-normal">{children}</div>
            </div>
        </Transition>
    )
}