import Button from "../form/Button";
import Dialog from "./Dialog";

interface Props {
    id: string;
    title: string;
    children: React.ReactNode;
    open: boolean;
    onClose: Function;
    onConfirm: Function;
}
export default function ConfirmDlg(props: Props) {
    const { open, onClose, title, children, onConfirm } = props;
    if (!open) {
        return <></>;
    }

    return (
        <Dialog open={open} onClose={onClose}>
            <h2 className="text-xl">{title}</h2>
            <div className="py-5">{children}</div>
            <div className="flex justify-end">
                <div className="p-1">
                    <Button
                        onClick={() => onClose(props.id)}
                        className="bg-slate-500"
                    >
                        No
                    </Button>
                </div>
                <div className="p-1">
                    <Button
                        className="bg-blue-500"
                        onClick={() => {
                            onClose(props.id);
                            onConfirm(props.id);
                        }}
                    >
                        Yes
                    </Button>
                </div>
            </div>
        </Dialog>
    );
}