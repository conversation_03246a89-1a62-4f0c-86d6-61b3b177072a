import IconButton from "../form/IconButton";

interface Props {
    children: React.ReactNode;
    open: boolean;
    onClose: Function;
}

export default function Dialog(props: Props) {
    const { open, onClose } = props;
    if (!open) {
        return <></>;
    }
    return (
        <div className="fixed inset-0 z-50 overflow-auto backdrop-brightness-50 flex">
            <div className="relative p-8 bg-white w-full max-w-md m-auto flex-col flex rounded-lg">
                <div>{props.children}</div>
                <span className="absolute top-0 right-0 p-4">
                    <IconButton onClick={() => onClose()}>
                        <svg
                            className="h-6 w-6 fill-current text-slate-500 hover:text-slate-700"
                            role="button"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                        >
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z" />
                        </svg>
                    </IconButton>
                </span>
            </div>
        </div>
    );
}