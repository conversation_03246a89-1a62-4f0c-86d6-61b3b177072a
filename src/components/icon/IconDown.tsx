interface Props {
    className?: string;
}
export const IconDown = (props: Props) => {
    return <svg xmlns="http://www.w3.org/2000/svg" className={`w-5 h-5 fill-current ${props.className}`} viewBox="0 0 24 24"><path className="heroicon-ui" d="M20 9a1 1 0 012 0v8a1 1 0 01-1 1h-8a1 1 0 010-2h5.59L13 10.41l-3.3 3.3a1 1 0 01-1.4 0l-6-6a1 1 0 011.4-1.42L9 11.6l3.3-3.3a1 1 0 011.4 0l6.3 6.3V9z" /></svg>
}