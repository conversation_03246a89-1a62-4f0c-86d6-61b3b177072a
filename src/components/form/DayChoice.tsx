import { ChangeEvent } from "react";

const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
interface Props {
    value: number[];
    onChange: (days: number[]) => void;
    className?: string;
}

export default function DayChoice(props: Props) {
    function handleChange(e: ChangeEvent<HTMLInputElement>) {
        let days: number[] = props.value
        if (!e.target.checked) {
            const index = days.indexOf(parseInt(e.target.value));
            if (index > -1) {
                days.splice(index, 1);
            }
        } else {
            days.push(parseInt(e.target.value))
        }
        props.onChange(days)
    }
    return (
        <ul className="grid grid-cols-4 gap-x-2">
            {days.map((day, i) => (
                <li key={i}>
                    <input type="checkbox" defaultChecked={props.value.includes(i)} id={i + "-option"} value={i + ""} onChange={handleChange} className="hidden peer" />
                    <label htmlFor={i + "-option"} className="bg-gray-100 my-1 w-full text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full inline-flex items-center justify-between p-3 border-2 border-gray-200 cursor-pointer peer-checked:border-blue-600 hover:text-black peer-checked:text-gray-600 hover:bg-white">
                        {day}
                    </label>
                </li>
            ))}
        </ul>
    )
}