import { ChangeEvent } from "react";

interface Props {
    value: string;
    onChange: (time: string) => void;
    className?: string;
}

export default function Timepicker(props: Props) {
    const hourMin = props.value.split(":");
    function handleChange(e: ChangeEvent<HTMLSelectElement>) {
        if (e.target.name === "hours") {
            hourMin[0] = e.target.value
        } else if (e.target.name === "minutes") {
            hourMin[1] = e.target.value
        }
        props.onChange(hourMin[0] + ":" + hourMin[1])
    }
    return (
        <div className="p-4 py-2 bg-white border border-slate-300">
            <div className="flex">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="text-slate-400 w-6 h-6 mr-3">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <select name="hours" onChange={handleChange} defaultValue={hourMin[0]} className="bg-transparent appearance-none outline-none">
                    {[...Array(24)].map((x, i) =>
                        <option key={i} value={i + 1}>{i + 1}</option>
                    )}
                </select>
                <span className="mr-3">:</span>
                <select name="minutes" onChange={handleChange} defaultValue={hourMin[1]} className="bg-transparent appearance-none outline-none mr-4">
                    {[...Array(31)].map((x, i) =>
                        <option key={i} value={i < 10 ? "0" + i : i}>{i < 10 ? "0" + i : i}</option>
                    )}
                </select>
            </div>
        </div>
    )
}