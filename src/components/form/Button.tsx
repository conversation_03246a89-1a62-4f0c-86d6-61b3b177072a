import { <PERSON>actN<PERSON>, MouseEvent } from "react";

interface Props {
    children: ReactNode;
    type?: 'submit' | 'button' | 'reset';
    disabled?: boolean;
    onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
    className?: string;
}

export default function Button(props: Props) {
    const { type = 'button', children, onClick, className = '' } = props;
    return (
        <button
            disabled={props.disabled}
            className={`inline-flex items-center text-white text-sm border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 focus:text-blue-700 font-bold py-2 px-4 focus:outline-none focus:shadow-outline ${className}`}
            type={type}
            onClick={onClick}
        >
            {children}
        </button>
    );
}