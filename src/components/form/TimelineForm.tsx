import { ChangeEvent, useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "./DayChoice";
import Timepicker from "./TimePicker";
import Button from "./Button";
import AlertBox from "../dialog/AlertBox";
import { Timeline, TimelineEdit } from "../../models/Timeline";
import { useAuth } from "../../provider";
import { useNavigate } from "react-router-dom";
import { handleServerError } from "../../models/Response";

interface Props {
    value?: Timeline;
}

export default function TimelineForm(props: Props) {
    const token = useAuth();
    const { value } = props;
    const navigate = useNavigate()
    const [days, setDays] = useState(value ? value.dates : [1, 2, 3, 4, 5])
    const [hours, setHours] = useState(value ? value.time : "10:00")
    const [desc, setDesc] = useState(value ? value.description : "")
    const [isCommand, setIsCommand] = useState(value ? value.command : false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [error, setError] = useState("")

    function handleSubmit(e: ChangeEvent<HTMLFormElement>) {
        e.preventDefault();
        setIsSubmitting(true);

        const requestBody: TimelineEdit = {
            time: hours,
            description: desc,
            is_command: isCommand,
            dates: days,
        }

        try {
            fetch(process.env.REACT_APP_API_URL + `/daily`, {
                method: 'PUT',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            })
                .then((res) => res.json())
                .then((data) => {
                    if (data.error) {
                        setError(handleServerError(data.error.message))
                    } else {
                        setError("");
                        navigate("/");
                    }
                    setIsSubmitting(false);
                });
        } catch (err) {
            console.log(err);
        }

    }

    return (
        <>
            <AlertBox id="alert-form" onClose={(_) => setError("")} visible={error !== ""}>
                {error}
            </AlertBox>
            <form onSubmit={handleSubmit} className="flex flex-col gap-3">
                <DayChoice value={days} onChange={(d: number[]) => setDays(d)} />
                <Timepicker value={hours} onChange={(h: string) => setHours(h)} />
                <input defaultValue={desc} onChange={(e: ChangeEvent<HTMLInputElement>) => setDesc(e.target.value)} className="border border-slate-300 px-4 py-2 focus:outline-none focus:border-blue-600 focus:ring-blue-600 focus:ring-1" type="text" placeholder="Description"></input>
                <label className="relative inline-flex items-center mb-4 cursor-pointer">
                    <input onChange={(e: ChangeEvent<HTMLInputElement>) => setIsCommand(e.target.checked)} type="checkbox" defaultChecked={isCommand} className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-blue-300  peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    <span className="ml-3 text-sm font-medium text-black">Is Command</span>
                </label>

                <Button disabled={isSubmitting} type="submit" className="bg-blue-600 rounded-lg text-white w-fit front-bold px-4 py-2">
                    {isSubmitting ?
                        <svg aria-hidden="true" role="status" className="inline mr-3 w-4 h-4 text-white animate-spin" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="#E5E7EB"></path>
                            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentColor"></path>
                        </svg>
                        : null
                    } Add
                </Button>
            </form>
        </>
    )
}