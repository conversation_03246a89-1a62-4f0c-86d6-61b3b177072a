import { useContext, useEffect, useState } from "react";
import { TokenContext } from "../../provider";
import { PortfolioWatching } from "../../models/Stock";
import { Loading } from "../Loading";
import { IconUp } from "../icon/IconUp";
import { IconDown } from "../icon/IconDown";
import { formatCurrency, formatNumber } from "../../libs/numberUtil";
import { IconEmpty } from "../icon/IconEmpty";

export const StockStat = () => {
    const token = useContext(TokenContext);
    const [data, setData] = useState<PortfolioWatching[]>([]);
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        try {
            fetch(process.env.REACT_APP_API_URL + `/portfolio/watching`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: PortfolioWatching[]) => {
                    if (data && data.length > 0) {
                        setData(data);
                    }
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token]);

    if (loading) {
        return <Loading />
    }

    if (data.length === 0) {
        return <IconEmpty />
    }

    return (
        <>
            <div className="mt-2 flex flex-wrap justify-center items-center gap-4">
                {data.map((stock) => (
                    <div
                        className="flex h-20 w-40 flex-col items-center justify-center rounded-md border border-dashed border-gray-200 transition-colors duration-100 ease-in-out hover:border-gray-400/80">
                        <div className="flex flex-row mt-2 text-sm text-gray-400">
                            {stock.price > stock.peak_price ?
                                <IconUp className="mr-3 fill-green-500/95" />
                                :
                                <IconDown className="mr-3 fill-red-500/95" />
                            }
                            <span>{stock.code}</span>

                        </div>
                        <div className="flex flex-row items-center justify-center text-gray-600 font-bold">
                            <span>{formatCurrency(stock.price, "VND")}</span>
                            <span>&nbsp;
                                ({formatNumber((stock.price / stock.book_price - 1) * 100)}%)
                            </span>
                        </div>
                        <div className={`flex flex-row items-center justify-center ${stock.price > stock.peak_price ? "text-green-500" : "text-red-500"}`}>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                            </svg>

                            {stock.price > stock.peak_price ?
                                formatCurrency(stock.peak_price, "VND")
                                :
                                formatCurrency(stock.bottom_price, "VND")
                            }
                        </div>

                    </div>
                ))}

            </div>
        </>
    );
};