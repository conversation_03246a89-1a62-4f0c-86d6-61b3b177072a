import { Link } from "react-router-dom";
import { formatCurrency, formatNumber } from "../../libs/numberUtil";
import { PortfolioStock } from "../../models/Stock";

export const StockCard = ({ info }: { info: PortfolioStock }) => {

    const bookPrice = (info.book_value / info.amount)
    const bookPriceSubDiv = (info.book_value - info.total_dividend) / info.amount
    const bookPriceSubDivVal = bookPriceSubDiv * info.amount
    const plPercent = info.fair_value / bookPriceSubDivVal - 1
    const pandl = info.fair_value - bookPriceSubDivVal
    const dividendRatio = info.dividend / bookPriceSubDiv
    const boughAt = new Date(info.buy_at)
    return (
        <>
            <Link to={`/portfolio-stock-events/${info.code}`} className="rounded shadow-xl overflow-hidden w-full flex">
                <div className="flex w-full p-10 bg-gray-100 text-gray-600 items-center">
                    <div className="w-full">
                        <div className="flex w-full space-x-2 items-center">
                            <img className="w-1/5" src={`https://finance.vietstock.vn/image/` + info.code} alt={info.code} />
                            <div className="w-4/5">
                                <h3 className="text-lg font-semibold leading-tight text-gray-800">{info.code}</h3>
                                <h6 className="text-sm leading-tight mb-2">
                                    <span>{boughAt.toLocaleString("vi-VN", { timeZone: "Asia/Ho_Chi_Minh" })}</span>
                                </h6>
                                <div className="flex w-full items-end mb-6">
                                    <span className="block leading-none text-3xl text-gray-800">{formatCurrency(info.price, "VND")}</span>
                                    <span className={`block leading-5 text-sm ml-4 ${plPercent < 0 ? "text-red-500" : "text-green-500"}`}>
                                        {`${plPercent < 0 ? '▼' : '▲'} ${formatCurrency(plPercent * bookPriceSubDiv, "VND")} (${formatNumber(plPercent * 100)}%)`}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div className="flex-col space-y-1">
                            <div className="grid grid-cols-1 gap-1 md:grid-cols-2 md:gap-3 w-full text-xs">
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Book price</div>
                                    <div className="flex-1 text-right">{formatCurrency(bookPrice, "VND")}</div>
                                </div>
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Amount</div>
                                    <div className="pl-3 text-right">{formatNumber(info.amount)}</div>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 gap-1 md:grid-cols-2 md:gap-3 w-full text-xs">
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Dividend</div>
                                    <div className="flex-1 text-right">{formatCurrency(info.total_dividend, "VND")}</div>
                                </div>
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Adjusted book price</div>
                                    <div className="pl-3 text-right">{formatCurrency(bookPriceSubDiv, "VND")}</div>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 gap-1 md:grid-cols-2 md:gap-3 w-full text-xs">
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Book value</div>
                                    <div className="flex-1 text-right">{formatCurrency(info.book_value, "VND")}</div>
                                </div>
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Fair value</div>
                                    <div className="pl-3 text-right">{formatCurrency(info.fair_value, "VND")}</div>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 gap-1 md:grid-cols-2 md:gap-3 w-full text-xs">
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">P&L</div>
                                    <div className={`flex-1 text-right ${pandl < 0 ? "text-red-500" : "text-green-500"}`}>{formatCurrency(pandl, "VND")}</div>
                                </div>
                                <div className="flex w-full">
                                    <div className="flex-1 text-left font-semibold">Dividend ratio</div>
                                    <div className="pl-3 text-right text-green-500">{formatNumber(dividendRatio * 100)}%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </Link>
        </>
    );
};