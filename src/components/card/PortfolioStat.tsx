import { Link } from "react-router-dom";
import { formatCurrency, formatNumber } from "../../libs/numberUtil";
import IconButton from "../form/IconButton";
import { IconDown } from "../icon/IconDown";
import { IconSlideway } from "../icon/IconSlideway";
import { IconUp } from "../icon/IconUp";

interface Props {
    title: string;
    displayValue: number;
    percentValue: number;
    link?: string;
}

export default function PortfolioStat(props: Props) {
    const { title, displayValue, percentValue } = props;

    let className = "text-gray-500"
    let icon = <IconSlideway />
    if (percentValue < 0) {
        className = "text-red-500"
        icon = <IconDown />
    } else if (percentValue > 0) {
        className = "text-green-500"
        icon = <IconUp />
    }
    return <div className="flex flex-col justify-center px-4 py-4 bg-white border border-gray-300 rounded">
        <div>
            <div>
                <p className={`flex items-center justify-end text-md ${className}`}>
                    <span className="font-bold">{formatNumber(percentValue * 100)}%</span>
                    {icon}
                </p>
            </div>
            <p className="text-3xl font-semibold text-center text-gray-800">{formatCurrency(displayValue, "VND")}</p>
            <p className="text-lg text-center text-gray-500">
                {title} {props.link ?
                    <Link to={props.link}>
                        <IconButton>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15m3 0l3-3m0 0l-3-3m3 3H9" />
                            </svg>
                        </IconButton>
                    </Link> : null
                }
            </p>
        </div>
    </div>
}