import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ChartDataset,
    ChartData,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { useAuth } from '../../provider';
import { useEffect, useState } from 'react';
import { Loading } from '../Loading';
import { DataPoint } from '../../models/Chart';

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

interface DataDistribution {
    dataTitle: string;
    distribution: string;
    bgColor: string;
}

interface Props {
    title: string;
    dataSet: DataDistribution;
    dataPoint?: DataPoint;
    xSuffix?: string;
}

export default function Histogram(props: Props) {
    const token = useAuth();
    const options = {
        responsive: true,
        scales: {
            x: {
                stacked: true,
            },
            y: {
                beginAtZero: true,
            },
        },
        plugins: {
            legend: {
                position: 'top' as const,
            },
            title: {
                display: true,
                text: props.title,
            },
        },
    };
    const [data, setData] = useState<ChartData<"bar", ChartDataset<"bar", number[]>[], unknown>>({
        datasets: []
    });
    const [loading, setLoading] = useState(true);
    useEffect(() => {
        try {
            const params = new URLSearchParams({ file_name: props.dataSet.distribution });
            fetch(process.env.REACT_APP_API_URL + `/stat/distribution?` + params.toString(), {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
            })
                .then((res) => res.json())
                .then((data: number[]) => {
                    data.sort((a, b) => a - b)
                    let min = data[0], max = data[data.length - 1]
                    var bucketStep = Math.floor((max - min) / 100) + 1;
                    let labels: string[] = [];
                    let bucketCount: any[] = [], bucketNegCount: any[] = [], bucketPosCount: any[] = [];
                    let startIdx = 0;
                    for (let i = min; i < max; i += bucketStep) {
                        labels.push(Math.round(i).toString() + props.xSuffix)
                        let total = 0
                        for (let j = startIdx; data[j] <= i; j++) {
                            total++
                            startIdx = j
                        }
                        if (props.dataPoint) {
                            if (i < -props.dataPoint.x) {
                                bucketNegCount.push(total)
                                bucketPosCount.push(0)
                                bucketCount.push(0)
                            } else if (i > props.dataPoint.x) {
                                bucketNegCount.push(0)
                                bucketPosCount.push(total)
                                bucketCount.push(0)
                            } else {
                                bucketNegCount.push(0)
                                bucketPosCount.push(0)
                                bucketCount.push(total)
                            }
                        } else {
                            bucketCount.push(total)
                        }
                    }
                    setData({
                        labels: labels,
                        datasets: [{
                            label: props.dataSet.dataTitle,
                            data: bucketCount,
                            backgroundColor: props.dataSet.bgColor,
                        }, {
                            label: props.dataPoint?.code,
                            data: bucketNegCount,
                            backgroundColor: props.dataPoint?.negativeColor,
                        }, {
                            label: props.dataPoint?.code,
                            data: bucketPosCount,
                            backgroundColor: props.dataPoint?.positiveColor,
                        }],
                    })
                    setLoading(false);
                });
        } catch (err) {
            console.log(err);
        }
    }, [token, props]);

    if (loading) {
        return <Loading />
    }
    return <Bar options={options} data={data} />
}